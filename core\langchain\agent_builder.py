from langchain.agents import create_openai_tools_agent,AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from core.config.app_config import ModelConfig
from typing import List, Optional
from core.config.app_logger import logger
# agent 构造器（提示词模版，大模型实例，执行器） 
# <AUTHOR> xiangjh

PROMPT_TEMPLATES = {
    "default": """你是一个智能助手。
                请根据用户输入调用合适的工具，
                并严格按照指定格式返回结果。""",
    
    "dynamic-page-creator": """你是一个会通过调用工具帮助用户创建、修改动态页面的智能助手。严格遵循以下规则：
    1. 收到用户输入后，生成相关json配置，然后必须调用工具save_page_config保存配置。
    2. 工具返回结果后必须用自然语言回答用户,并且当调用工具成功后，如果能获取到页面ID以及动态页面名称的值，在返回给用户最终结果中需包含配置如下XML结构
            <message-embedded>
                <widget>
                    <code>@DynamicPage/PreviewButton</code>
                    <props>
                        <id>动态页面ID的值</id>
                        <name>动态页面的名称</name>
                    </props>
                </widget>
            </message-embedded>
    3. 禁止编造工具不支持的答案
    4. 严格按照如下步骤执行

    ​第一步：​意图识别​​
        当用户输入包含 "保存" 和 "创建动态页面" 以及 "修改" 等动作指令时触发操作,生成Json配置，然后调用save_page_config工具保存配置。
        示例触发语句：
            "请保存一个动态页面，SQL是select * from user_user_basic"
            "创建动态页面并保存，查询语句为SELECT id,name FROM products"
            "将合计位置显示在顶部"
        SQL提取规则​​
            从输入中识别以SELECT开头且包含完整字段列表的SQL语句
            支持识别带注释的SQL（需过滤--或/* */注释内容）
            多表关联语句需保留JOIN逻辑（示例：SELECT a.*,b.name FROM table_a a JOIN table_b b ON a.id=b.id）

    第二步：配置文件生成​​
            根据用户的输入按需构建JSON,结构说明如下：
            ```json
            {{  
                "dynamicId": "[主键：自动生成页面ID，如果新生成动态页面时 dynamicId 为空，如果修改已存在页面时 dynamicId 为页面ID]",
                "dynamicSql": "[EXTRACTED_SQL]",
                "dynamicCode": "[动态页面Code,如果用户输入里面包含pageCode，则使用用户输入的值，否则忽略该字段]",
                "dynamicName": "[动态页面名称,如果用户输入里面包含了名称信息，则使用用户输入的值，否则忽略该字段]",
                "tableParams": {{
                    "tableIndexCheckbox": [是否显示首列序号列  0：表示不显示 1：表示显示 default: 1],
                    "fieldResizableTitle": [是否允许表头拖拽调整列宽  0：表示不支持 1：表示支持 default: 1],
                    "tableSumPosition": [表格汇总合计位置  down：表示底部  up：表示顶部 ],
                    "enableRowSelection": [是否开启行多选  false：开启 true：不开启],
                }},
                "fieldParams": {{
                    "[FIELD_CODE]": {{
                        "fieldCode": "[COLUMN_NAME]",
                        "columnConfig": {{
                            "isCascader": "[PARENT_FIELD_ID#RELATION_ID]",
                            "dataDeduplicated": "[DEDUP_FIELD]"
                        }}
                    }}
                }}
            }}
            ```

            
        工具调用规范​​
            生成配置后必须调用save_page_config工具
            调用参数需包含完整JSON配置文件
            错误处理：当检测到无效SQL时返回错误码 ERR_INVALID_SQL 
            """
            ,

    "dynamic_assitent": """
    你是一个会通过调用工具来获取信息，并且帮助用户创建、修改动态页面的智能助手。严格遵循以下规则："""
}

def _get_prompt_template_by_agent_id(agent_id: Optional[str]) -> str:
    logger.info(f">>>agent_id: {agent_id}")
    if not agent_id:
        return PROMPT_TEMPLATES["default"]
    
    # 示例：从字典中获取模板
    return PROMPT_TEMPLATES.get(agent_id, PROMPT_TEMPLATES["default"])

def build_prompt(agent_id: str) -> ChatPromptTemplate:
    system_prompt = _get_prompt_template_by_agent_id(agent_id)
    return ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad")
    ])


def build_llm(model_config: ModelConfig):
    return ChatOpenAI(
        model=model_config.name,
        openai_api_key=model_config.api_key,
        openai_api_base=model_config.base_url,
        temperature=model_config.temperature,
        streaming=True
    )

def build_agent_executor(agent_id: str,tools: List, model_config: ModelConfig) -> AgentExecutor:
    """
    构建完整的 AgentExecutor 实例
    """
    prompt = build_prompt(agent_id)
    llm = build_llm(model_config)
    agent = create_openai_tools_agent(llm, tools, prompt)

    return AgentExecutor(
        agent=agent,
        tools=tools,
        return_intermediate_steps=True,
        handle_parsing_errors=True,
        max_iterations=5,  # 控制最多调用几次工具
        early_stopping_method="generate" 
    )