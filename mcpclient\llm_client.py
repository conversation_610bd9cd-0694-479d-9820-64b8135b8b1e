from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langchain_core.messages import trim_messages
from core.config.app_config import config
from core.langchain.custom_runnable_with_history import RunnableWithCustomHistory 
from core.langchain.agent_builder import build_agent_executor
from core.langchain.event_processor import process_agent_stream,safe_json_dumps
from core.langchain.tool_registry import register_tools
from core.langchain.history_manager import HistoryManager
from typing import Optional, List, Dict, Any, AsyncGenerator, Union
from fastapi import Request
from mcpclient.mcp_client import Mcp<PERSON>lient
from langchain_core.tools import tool
import traceback
from utils.common_utils import CommonUtils
safe_json_dumps = CommonUtils.safe_json_dumps
from core.config.app_logger import logger

class LLMClient:
    def __init__(
        self,
        request: Request,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
    ):
        self.mcp_client: McpClient = request.app.state.mcp_client
        self.model_config = config.model
        self.model_name = model_name or self.model_config.name
        self.api_key = api_key or self.model_config.api_key
        self.base_url = base_url or self.model_config.base_url
        self.user_id = user_id
        self.agent_id = agent_id
        self.tools = []
        self.history = None
        self.history_manager = HistoryManager(user_id=user_id, agent_id=agent_id)
        
        if not self.model_name:
            raise ValueError("model_name 必须指定或在配置中定义")

    async def init_async(self):
        """异步初始化"""
        await self._init_tools()
        await self._init_agent()

    async def _init_tools(self):
        """初始化工具集"""
        self.tools = []
        if self.mcp_client and self.mcp_client.session:
            try:
                tools_response = await self.mcp_client.session.list_tools()
                self.tools = register_tools(self.mcp_client, tools_response)
            except Exception as e:
                logger.error(f"工具初始化失败: {str(e)}")
                traceback.print_exc()

    async def _init_agent(self):
        self.agent_executor = build_agent_executor(self.agent_id,self.tools, self.model_config)
        self.chain_with_history = RunnableWithCustomHistory(
            self.agent_executor,
            get_session_history=self.history_manager.get_redis_history,
            input_messages_key="input",
            history_messages_key="chat_history"
        ) 

    
    #####################################################################################################################
    # 大模型流式调用入口，返回按事件处理后的数据

    # {"type": "tool_start", "tool": "get_weather", "input": {"kwargs": {"location": "上海"}}}
    # {"type": "tool_result", "tool": "get_weather", "output": "[TextContent(type='text', text='上海当前天气：25℃，晴', annotations=None)]"}
    # {"type": "stream", "content": "上海"}
    # {"type": "stream", "content": "当前"}
    # {"type": "stream", "content": "天气"}
    # {"type": "stream", "content": "25"}
    # {"type": "stream", "content": "度"}
    # 
    #####################################################################################################################
    async def chat_stream(self, question: str, session_id: str = "default") -> AsyncGenerator[Union[str, dict], None]:
        try:
            history = self.history_manager.get_redis_history(session_id)
            # 获取完整历史并过滤掉 ToolMessage
            raw_messages = await history.aget_messages()
            chat_history = [msg for msg in raw_messages if not isinstance(msg, ToolMessage)]

            input_context = {
                "input": question,
                "chat_history": chat_history
            }

            config = {
                "configurable": {
                    "session_id": session_id,
                    "user_id": self.user_id,
                    "agent_id": self.agent_id
                }
            }

            async for chunk in process_agent_stream(
                self.chain_with_history,
                input_context,
                config,
                history,
                question,
                session_id
            ):
                logger.info(f"响应: {chunk}")
                yield chunk

        except Exception as e:
            logger.error(f"流式处理异常: {str(e)}")
            yield  safe_json_dumps({
                "type": "error",
                "message": "对话处理中断，请稍后再试"
            })
            traceback.print_exc()

        finally:
            yield  safe_json_dumps({
                "type": "stream",
                "content": " "
            })
            logger.info(">>>>>>流式响应结束<<<<<<<<<<<<")
        