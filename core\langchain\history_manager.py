from core.langchain.redis_with_postgres import RedisWithPostgreSQLHistory
from langchain_core.messages import ToolMessage
from core.config.app_config import config

# 历史记录管理类
# <AUTHOR> xiangjh

class HistoryManager:
    def __init__(self, user_id=None, agent_id=None):
        self.user_id = user_id
        self.agent_id = agent_id

    def get_redis_history(self, session_id: str) -> RedisWithPostgreSQLHistory:
        redis_config = config.redis
        host = redis_config.host
        port = redis_config.port
        password = redis_config.password
        db = redis_config.db

        redis_url = f"redis://{host}:{port}/{db}" if not password else f"redis://:{password}@{host}:{port}/{db}"

        return RedisWithPostgreSQLHistory(
            session_id=session_id,
            user_id=self.user_id,
            agent_id=self.agent_id,
            url=redis_url,
            ttl=28800
        )

    async def load_chat_history(self, session_id: str):
        history = self.get_redis_history(session_id)
        raw_messages = await history.aget_messages()
        return [msg for msg in raw_messages if not isinstance(msg, ToolMessage)], history