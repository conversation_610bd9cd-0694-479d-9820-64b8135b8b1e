import asyncio
from uuid import uuid4
from datetime import datetime, timezone

from core.config.security import check_user
from core.vo.user_vo import UserVO
from fastapi import APIRouter, Body, Depends, FastAPI
from fastapi.responses import StreamingResponse

from core.message.transmitter import Transmitter
from core.message.types import MessageType, MessagePackage
from core.services.database.schemas.message import MessageCreate
from core.services.database.schemas.conversation import ConversationCreate
from core.services.database import db_manager
from core.services.database.crud.conversation import conversation_curd
from core.services.database.crud.message import message_curd

mock_message_chunks = [
    {
        "data": "I am",
        "is_last": False,
        "package_type": 2,
        "is_new_package": True,
    },
    {
        "data": "Thinking...",
        "is_last": False,
        "package_type": 2,
        "is_new_package": False,
    },
    {
        "data": "Thinking......",
        "is_last": False,
        "package_type": 2,
        "is_new_package": False,
    },
    {
        "data": "Thinking End",
        "is_last": True,
        "package_type": 2,
        "is_new_package": False,
    },
    {
        "data": "hi",
        "is_last": False,
        "package_type": 0,
        "is_new_package": True,
    },
    {
        "data": "hello",
        "is_last": False,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": """
        111111111
            <message-embedded>
                <widget>
                    <code>@DynamicPage/PreviewButton</code>
                    <props>
                        <id>111111111111</id>
                        <name>测试动态页面</name>
                    </props>
                </widget>
            </message-embedded>
            2222222222
            """,
        "is_last": False,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": "what's your name?",
        "is_last": False,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": "my name is test",
        "is_last": False,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": "nice to meet you",
        "is_last": True,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": "hi1111111111111111",
        "is_last": True,
        "package_type": 0,
        "is_new_package": True,
    },
    {
        "data": "hello",
        "is_last": False,
        "package_type": 0,
        "is_new_package": True,
    },
    {
        "data": """
            111111111
            <message-embedded>
                <widget>
                    <code>@DynamicPage/PreviewButton</code>
                    <props>
                        <id>111111111111</id>
                    </props>
                </widget>
            </message-embedded>
            2222222222
            """,
        "is_last": False,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": "# GFM\n\n## Autolink literals\n\nwww.example.com, https://example.com, and <EMAIL>.\n\n## Footnote\n\nA note[^1]\n\n[^1]: Big note.\n\n## Strikethrough\n\n~one~ or ~~two~~ tildes.\n\n## Table\n\n| a | b  |  c |  d  |\n| --- | --- | --- | --- |\n| a | b  |  c |  d  |\n\n## Tasklist\n\n* [ ] to do\n* [x] done",
        "is_last": False,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": "what's your name?",
        "is_last": True,
        "package_type": 0,
        "is_new_package": False,
    },
    {
        "data": '{"type": "command", "commands": []}',
        "is_last": True,
        "package_type": 1,
        "is_new_package": True,
    },
]


async def event_generator(transmitter: Transmitter):
    yield transmitter.start()
    await asyncio.sleep(0.1)
    for chunk in mock_message_chunks:
        if chunk:
            # Convert the chunk to JSON and format as SSE
            yield transmitter.send_message(
                data=chunk["data"],
                package_type=chunk["package_type"],
                is_last=chunk["is_last"],
                is_new_package=chunk["is_new_package"],
            )
        await asyncio.sleep(0.1)

    # Send a final message to indicate completion
    yield await transmitter.end()


class TestAgent:
    app: FastAPI
    router: APIRouter

    def __init__(self, app: FastAPI):
        self.app = app
        self.router = APIRouter()
        self.add_api(self.router)
        app.include_router(self.router, tags=["Test"])

    def add_api(self, router: APIRouter):
        @router.post("/api/chat/test")
        async def test(
            data: dict = Body(..., description="用户提问内容"),
            user: UserVO = Depends(check_user),
        ):

            print(data)
            agent_code = data["agent_code"]
            # 根据conversation_id查询会话，如果没有则创建一个
            conversation_id = data.get("conversation_id")
            if not conversation_id:
                async with db_manager.session() as session:
                    new_conversation = ConversationCreate(
                        user_id=user.userId,
                        title="test",
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                        current_agent_code=agent_code,
                    )
                    new_conversation = await conversation_curd.create(
                        db=session, obj_input=new_conversation
                    )
                    conversation_id = new_conversation.id

            # 保存用户消息
            message_pkg = MessagePackage(
                package_id=0,
                package_type=0,
                status=1,
                data=data["message"],
            )
            async with db_manager.session() as session:
                new_message = MessageCreate(
                    agent_code=agent_code,
                    conversation_id=conversation_id,
                    message_type=MessageType.HUMAN,
                    content=[message_pkg.model_dump()],
                )
                await message_curd.create(db=session, obj_input=new_message)

                transmitter = Transmitter(
                    conversation_id=conversation_id,
                    message_id=new_message.id,
                    agent_code=agent_code,
                )

            return StreamingResponse(
                event_generator(transmitter),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                },
            )
