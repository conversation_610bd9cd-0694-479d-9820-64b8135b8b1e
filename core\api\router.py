from fastapi import APIRouter, Depends

from core.api.login import router as login_router
from core.api.conversation import router as conversation_router
from core.api.message import router as message_router
from core.api.chat import router as chat_router

from core.config.security import check_user

# 私有路由，所有请求都必须进行权限校验
router = APIRouter(dependencies=[Depends(check_user)], tags=["Private"])

# 注册会话历史相关的路由
router.include_router(conversation_router)
# 注册消息相关的路由
router.include_router(message_router)
# 注册聊天相关的路由
router.include_router(chat_router)

# 公开路由，不进行权限校验
router_public = APIRouter(tags=["Public"])
# 注册登录相关的路由
router_public.include_router(login_router)
