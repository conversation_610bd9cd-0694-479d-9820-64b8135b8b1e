from typing import Any
from mcp.server.fastmcp import FastMCP
import requests
import json
from pydantic import BaseModel

# 定义MCP 服务器，提供工具和资源，将远程的调用都封装在工具函数中
# 注意：MCP 服务器需要和 MCP 客户端在同一台机器上运行，并且采用最高效的stdio传输方式
# @Author: xiangjh

# Initialize FastMCP server
mcp = FastMCP(
    name="McpServer",
    description="一个带有工具、资源和中文提示词的简单 MCP 服务器",
    instructions="""
          这是一个简单的 MCP 服务器，提供以下功能：
        - 资源：
          * stats://server：获取服务器运行时统计信息。
          * langs://available：获取支持的语言列表。
          * info://server：获取服务器基本信息。
        - 工具：
          * say_hello(name: str)：生成个性化的中文问候语并记录连接次数。
          * get_string_length(text: str)：分析输入文本的长度和特性。
          * get_weather(location: str)：Get current weather for a location。
          * order_hotel(city: str)：Book hotels in designated cities。
          * save_page_config(page_id: str, config_json: dict)modifies the configuration information of dynamic pages by providing a page_id and configuring JSON objects.
        - 提示词：
          * greeting_prompt(name: str)：生成中文问候语模板。
        
    """
)

class GetWeatherInput(BaseModel):
    location: str

class GetHotelInput(BaseModel):
    city: str

# 定义工具函数实现
@mcp.tool()
async def get_weather(**kwargs: Any) -> str:
    """
    Get current weather for a location
    Parameters:
        location (str): The location to get the weather for.
        example: 参数：{'kwargs': {'location': '武汉'}}
    Returns:
        str: The current weather information.
    """
    # 这里可以替换为真实的API调用
    try:
        input_dict = kwargs.get("kwargs", {})
        input_data = GetWeatherInput(**input_dict)
        location = input_data.location
        print(f"查询{location}天气")
        return f"{location}当前天气：25℃，晴"
    except Exception as e:
        return f"参数错误: {str(e)}"

# 添加order_hotel函数实现
@mcp.tool()
async def order_hotel(**kwargs: Any) -> str:
    """
    Book hotels in designated city
    Parameters:
        city (str): The city to book hotels in.
        example: 参数：{'kwargs': {'city': '武汉'}}
    Returns:
        str: The booking result.
    """
    input_dict = kwargs.get("kwargs", {})
    input_data = GetHotelInput(**input_dict)
    city = input_data.city
    print(f"正在预订{city}的酒店")
    return f"{city}的酒店预订成功"



@mcp.tool()
async def save_page_config(**kwargs: Any) -> str:  # 修改返回类型
    """
    This tool create or update the configuration information of dynamic pages by providing a page_id and configuring JSON objects.
   
    Parameters:
        page_id (str):  Page ID.
        config_json (dict):  Configure JSON objects.
    return:
        json:  {\"success\":true,\"data\":{\"dynamicId\":\"42e1664b-56f0-4b55-b729-6949f30b6b34\",\"dynamicName\":\"未命名_202505281622101\"},\"page\":null,\"errorCode\":null,\"errorMessage\":null}.
    
    """
    param_dict = kwargs.get("kwargs", {})
    page_id = param_dict.get("page_id")
    config_json = param_dict.get("config_json")
    try:
        if not isinstance(config_json, dict):
            raise ValueError("配置必须为JSON对象")
        
        if page_id is not None:  
            config_json["dynamicId"] = page_id
            
        print(f"正在更新页面 {page_id} 的配置") 
        url = "http://************:8002/api/page/system/dynamic/page/manage/insertSqlWithTempForAi"
        #url = "http://localhost:7001/api/page/system/dynamic/page/manage/insertSqlWithTempForAi"
        
        response = requests.post(
            url,
            json=config_json,
            timeout=10
        )
        # {\"success\":true,\"data\":\"42e1664b-56f0-4b55-b729-6949f30b6b34\",\"page\":null,\"errorCode\":null,\"errorMessage\":null}
        if response.status_code == 200:
            return f"{response.text}"  # 直接返回字符串
        else:
            return f"接口返回错误: {response.status_code}\n{response.text}"
        #return f"页面 {page_id} 配置更新成功"  # 直接返回字符串
    except requests.exceptions.RequestException as e:
        return f"网络连接异常: {str(e)}"
    except json.JSONDecodeError:
        return "接口返回数据解析失败"

    

if __name__ == "__main__":
    mcp.run(transport='stdio')