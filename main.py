# 启动服务 确保 PYTHONPATH 包含项目根目录或 src 目录
# set PYTHONPATH=d:\projects\agent-server\src
# uvicorn src.core.main:app --reload --port 8000 --host 0.0.0.0

# 使用 curl 或 Postman 发送含有 Authorization 头的请求：
# curl -H "Authorization: Bearer CF1f-M0MRKFoqNJmYJyO8zjoxfQ" http://localhost:8000/admin/data
# <AUTHOR> xiangjh
# pylint: disable=wrong-import-position

import uvicorn
from dotenv import load_dotenv
load_dotenv()

from playground.agents.test import TestAgent
from core.server.app import create_app
from core.api.router import router as api_router,router_public

app = create_app(routers=[api_router, router_public])

test_agent = TestAgent(app)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
