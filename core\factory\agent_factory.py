from typing import Callable, Dict, Any
from core.config.app_config import config
from core.api.dynaimic_assistant import dify_generator_and_send
from core.api.dynaimic_creator import event_generator_and_send
from core.config.app_logger import logger


# Agent 工厂类,加载所有的agent,并注册到工厂中
# <AUTHOR> xiangjh
class AgentFactory:
    _senders: Dict[str, Callable] = {}

    @classmethod
    def register_sender(cls, agent_code: str, sender: Callable):
        cls._senders[agent_code] = sender

    @classmethod
    def register_senders(cls, senders: Dict[str, Callable]):
        cls._senders.update(senders)

    @classmethod
    def get_sender(cls, agent_code: str) -> Callable:
        """
        根据 agent_code 获取对应的 sender 方法。
        
        :param agent_code: Agent 的唯一标识符。
        :return: 匹配的 sender 方法。
        :raises ValueError: 如果未找到匹配的 sender。
        """
        sender = cls._senders.get(agent_code)
        if not sender:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的 sender")
        return sender

    @classmethod
    def load_senders(cls):
        """从配置文件动态加载 sender 方法映射"""
        agent_mapping = {}
        for agent in config.agents:
            try:
                sender = cls._get_sender_by_target(agent.target)
                agent_mapping[agent.name] = sender
                logger.info(f"成功注册 agent: {agent.name}, target: {agent.target}")
            except ValueError as e:
                logger.warning(f"跳过 agent '{agent.name}': {str(e)}")

        cls.register_senders(agent_mapping)

    @classmethod
    def _get_sender_by_target(cls, target: str):
        """根据 target 字段返回对应的 sender 方法"""
        if target == "dify":
            return dify_generator_and_send
        elif target == "langchain":
            return event_generator_and_send
        else:
            raise ValueError(f"未知的 target 类型: {target}")

    @classmethod
    def _get_default_sender(cls):
        return event_generator_and_send  