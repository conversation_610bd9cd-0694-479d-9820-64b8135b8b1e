from contextlib import asynccontextmanager
from typing import List
from fastapi import APIRouter, FastAPI
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from mcpclient.mcp_client import McpClient
from core.services.database import db_manager
from core.config.app_logger import logger
from core.factory.agent_factory import AgentFactory


def create_app(routers: List[APIRouter]) -> FastAPI:

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        logger.info("应用启动中... ")
        logger.info("PostgreSQL 初始化")
        await db_manager.init_db()
        logger.info("PostgreSQL 初始化完成")

        logger.info("Agents 初始化")
        AgentFactory.load_senders()
        logger.info("Agents 初始化完成")

        logger.info("MCPClient 初始化")
        mcp_client = McpClient()
        await mcp_client.connect_to_server("mcpserver/mcp_server.py")
        app.state.mcp_client = mcp_client
        logger.info("MCPClient 初始化完成")

        yield  # 应用运行期间

        logger.info("应用关闭中... 释放资源... ")
        # DatabaseManager doesn't have an explicit close method based on the provided code
        # The engine's connection pool will be disposed when the application exits
        logger.info("应用关闭完成")

    app = FastAPI(lifespan=lifespan)
    for router in routers:
        app.include_router(router, prefix="/api")

    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request, exc):
        return JSONResponse(
            status_code=exc.status_code,
            content={"code": exc.status_code, "message": exc.detail},
        )

    return app
